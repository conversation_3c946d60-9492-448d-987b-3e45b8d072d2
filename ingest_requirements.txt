# Core dependencies for document ingestion system
pydantic>=2.0.0
structlog>=23.0.0
python-magic>=0.4.27
chardet>=5.0.0

# File processing
PyMuPDF>=1.23.0  # PDF processing
python-docx>=0.8.11  # Word documents
python-pptx>=0.6.21  # PowerPoint presentations
pandas>=2.0.0  # Excel and data processing
openpyxl>=3.1.0  # Excel .xlsx files
xlrd>=2.0.0  # Excel .xls files
beautifulsoup4>=4.12.0  # HTML parsing
readability-lxml>=0.8.1  # HTML content extraction

# Image processing and OCR
Pillow>=10.0.0  # Image processing
opencv-python>=4.8.0  # Computer vision
pytesseract>=0.3.10  # OCR

# Document image extraction
PyMuPDF>=1.23.0  # PDF image extraction
python-docx>=0.8.11  # Word document image extraction
python-pptx>=0.6.21  # PowerPoint image extraction

# Language processing
langdetect>=1.0.9  # Language detection

# Vector storage and embeddings (OPTIMIZED)
openai>=1.0.0  # OpenAI API
# faiss-cpu>=1.7.4  # REMOVED - 2GB+ dependency, using pgvector instead
tiktoken>=0.5.0  # Token counting

# Cloud storage
boto3>=1.28.0  # AWS S3 support

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
black>=23.0.0  # Code formatting
isort>=5.12.0  # Import sorting
flake8>=6.0.0  # Linting
mypy>=1.5.0  # Type checking

# Optional dependencies for enhanced functionality
# Uncomment as needed:

# Advanced PDF processing
# tabula-py>=2.7.0  # PDF table extraction
# camelot-py[cv]>=0.10.1  # Advanced PDF table extraction
# pdfplumber>=0.9.0  # Alternative PDF processing

# Advanced image processing
# easyocr>=1.7.0  # Alternative OCR engine
# paddleocr>=2.7.0  # Another OCR option

# Document conversion
# python-libreoffice>=0.1.0  # LibreOffice integration for DOC/PPT conversion

# Advanced language processing
# spacy>=3.6.0  # Advanced NLP
# transformers>=4.30.0  # Hugging Face transformers

# Database alternatives
# chromadb>=0.4.0  # Alternative vector database
# pinecone-client>=2.2.0  # Pinecone vector database
# weaviate-client>=3.22.0  # Weaviate vector database

# Monitoring and observability
# prometheus-client>=0.17.0  # Metrics
# sentry-sdk>=1.28.0  # Error tracking
