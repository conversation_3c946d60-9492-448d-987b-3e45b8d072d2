"""
Analytics Service
Business logic for dashboard analytics including question bank analytics
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc

from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.models.lead_reference import QuestionBank, EscalationQuestionBank, LeadStatus
from app.schemas.analytics import (
    DashboardAnalyticsResponse,
    AnalyticsCountsData,
    RecentActivityItem,
    ChartDataPoint,
    DetailedAnalyticsRow,
    AnalyticsFilterOptions
)
from app.core.utils.exception_manager.custom_exceptions import DatabaseError
from app.services.analytics_cache import cache_analytics_result

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Service for handling analytics operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    def _get_date_range(self, date_range: str, custom_start: Optional[datetime] = None, custom_end: Optional[datetime] = None) -> Tuple[datetime, datetime]:
        """Get start and end dates based on date range filter"""
        now = datetime.now()
        
        if date_range == "last_7_days":
            start_date = now - timedelta(days=7)
            end_date = now
        elif date_range == "last_30_days":
            start_date = now - timedelta(days=30)
            end_date = now
        elif date_range == "last_3_months":
            start_date = now - timedelta(days=90)
            end_date = now
        elif date_range == "last_6_months":
            start_date = now - timedelta(days=180)
            end_date = now
        elif date_range == "last_year":
            start_date = now - timedelta(days=365)
            end_date = now
        elif date_range == "custom" and custom_start and custom_end:
            start_date = custom_start
            end_date = custom_end
        else:
            # Default to last 7 days
            start_date = now - timedelta(days=7)
            end_date = now
        
        return start_date, end_date
    
    def _calculate_percentage_change(self, old_value: int, new_value: int) -> float:
        """Calculate percentage change between two values"""
        if old_value == 0:
            return 100.0 if new_value > 0 else 0.0
        return ((new_value - old_value) / old_value) * 100
    
    def _format_date_for_period(self, date: datetime, time_period: str) -> str:
        """Format date based on time period grouping"""
        if time_period == "day":
            return date.strftime("%b %d")  # "May 1"
        elif time_period == "week":
            # Get week start (Monday)
            week_start = date - timedelta(days=date.weekday())
            return f"Week of {week_start.strftime('%b %d')}"
        elif time_period == "month":
            return date.strftime("%b %Y")  # "May 2024"
        elif time_period == "year":
            return date.strftime("%Y")  # "2024"
        else:
            return date.strftime("%b %d")
    
    @cache_analytics_result(ttl_minutes=3)  # Cache for 3 minutes
    async def get_analytics_counts(self, start_date: datetime, end_date: datetime) -> AnalyticsCountsData:
        """Get analytics counts data - OPTIMIZED VERSION with fallback"""
        try:
            # Calculate previous period for comparison
            period_length = end_date - start_date
            prev_start = start_date - period_length
            prev_end = start_date

            # OPTIMIZATION: Use separate optimized queries (more reliable)
            total_leads_result = await self.db.execute(
                select(func.count(Lead.id)).where(Lead.is_deleted == False)
            )
            total_leads = total_leads_result.scalar() or 0

            current_leads_result = await self.db.execute(
                select(func.count(Lead.id)).where(
                    Lead.is_deleted == False,
                    Lead.created_at >= start_date,
                    Lead.created_at <= end_date
                )
            )
            current_leads = current_leads_result.scalar() or 0

            prev_leads_result = await self.db.execute(
                select(func.count(Lead.id)).where(
                    Lead.is_deleted == False,
                    Lead.created_at >= prev_start,
                    Lead.created_at < prev_end
                )
            )
            prev_leads = prev_leads_result.scalar() or 0
            # Calculate leads percentage change using the optimized data
            leads_change_percent = self._calculate_percentage_change(prev_leads, current_leads)

            # OPTIMIZATION: Use separate optimized queries (more reliable)
            total_franchisors_result = await self.db.execute(
                select(func.count(Franchisor.id)).where(Franchisor.is_deleted == False)
            )
            total_franchisors = total_franchisors_result.scalar() or 0

            current_franchisors_result = await self.db.execute(
                select(func.count(Franchisor.id)).where(
                    Franchisor.is_deleted == False,
                    Franchisor.created_at >= start_date,
                    Franchisor.created_at <= end_date
                )
            )
            current_franchisors = current_franchisors_result.scalar() or 0

            prev_franchisors_result = await self.db.execute(
                select(func.count(Franchisor.id)).where(
                    Franchisor.is_deleted == False,
                    Franchisor.created_at >= prev_start,
                    Franchisor.created_at < prev_end
                )
            )
            prev_franchisors = prev_franchisors_result.scalar() or 0

            # Calculate franchisors percentage change
            franchisors_change_percent = self._calculate_percentage_change(prev_franchisors, current_franchisors)
            
            # OPTIMIZATION: Use separate optimized queries for better performance
            current_questions_result = await self.db.execute(
                select(func.count(QuestionBank.id)).where(
                    QuestionBank.is_deleted == False,
                    QuestionBank.created_at >= start_date,
                    QuestionBank.created_at <= end_date
                )
            )
            current_questions = current_questions_result.scalar() or 0

            prev_questions_result = await self.db.execute(
                select(func.count(QuestionBank.id)).where(
                    QuestionBank.is_deleted == False,
                    QuestionBank.created_at >= prev_start,
                    QuestionBank.created_at < prev_end
                )
            )
            prev_questions = prev_questions_result.scalar() or 0

            current_escalations_result = await self.db.execute(
                select(func.count(EscalationQuestionBank.id)).where(
                    EscalationQuestionBank.is_deleted == False,
                    EscalationQuestionBank.created_at >= start_date,
                    EscalationQuestionBank.created_at <= end_date
                )
            )
            current_escalations = current_escalations_result.scalar() or 0

            prev_escalations_result = await self.db.execute(
                select(func.count(EscalationQuestionBank.id)).where(
                    EscalationQuestionBank.is_deleted == False,
                    EscalationQuestionBank.created_at >= prev_start,
                    EscalationQuestionBank.created_at < prev_end
                )
            )
            prev_escalations = prev_escalations_result.scalar() or 0

            # Calculate totals
            total_questions = current_questions + current_escalations
            prev_total_questions = prev_questions + prev_escalations
            questions_change_percent = self._calculate_percentage_change(prev_total_questions, total_questions)
            
            # Calculate averages per day
            period_days = max(1, (end_date - start_date).days)
            avg_questions_per_day = total_questions / period_days
            avg_escalations_per_day = current_escalations / period_days
            
            # Static values for meetings
            total_meetings = 0
            meetings_change_percent = 0.0
            
            return AnalyticsCountsData(
                total_leads=total_leads,
                total_franchisors=total_franchisors,
                total_questions=total_questions,
                total_meetings=total_meetings,
                leads_change_percent=leads_change_percent,
                franchisors_change_percent=franchisors_change_percent,
                questions_change_percent=questions_change_percent,
                meetings_change_percent=meetings_change_percent,
                avg_questions_per_day=round(avg_questions_per_day, 1),
                avg_escalations_per_day=round(avg_escalations_per_day, 1)
            )
            
        except Exception as e:
            logger.error(f"Error getting analytics counts: {e}", exc_info=True)
            raise DatabaseError(
                error_key="ANALYTICS_COUNTS_FAILED",
                message="Failed to retrieve analytics counts"
            )
    
    @cache_analytics_result(ttl_minutes=2)  # Cache for 2 minutes
    async def get_recent_activity(self) -> List[RecentActivityItem]:
        """Get recent activity items - OPTIMIZED VERSION"""
        try:
            activities = []

            # OPTIMIZATION: Get all recent activities in parallel using asyncio.gather
            import asyncio

            # Define optimized queries
            latest_lead_query = select(Lead).where(
                Lead.is_deleted == False
            ).order_by(desc(Lead.created_at)).limit(1)

            latest_franchisor_query = select(Franchisor).where(
                Franchisor.is_deleted == False
            ).order_by(desc(Franchisor.created_at)).limit(1)

            latest_question_query = select(QuestionBank).where(
                QuestionBank.is_deleted == False
            ).order_by(desc(QuestionBank.created_at)).limit(1)

            latest_escalation_query = select(EscalationQuestionBank).where(
                EscalationQuestionBank.is_deleted == False
            ).order_by(desc(EscalationQuestionBank.created_at)).limit(1)

            # Execute all queries in parallel
            results = await asyncio.gather(
                self.db.execute(latest_lead_query),
                self.db.execute(latest_franchisor_query),
                self.db.execute(latest_question_query),
                self.db.execute(latest_escalation_query),
                return_exceptions=True
            )

            # Process lead result
            if not isinstance(results[0], Exception):
                latest_lead = results[0].scalar_one_or_none()
                if latest_lead:
                    lead_name = f"{latest_lead.first_name} {latest_lead.last_name or ''}".strip()
                    activities.append(RecentActivityItem(
                        id=str(latest_lead.id),
                        type="lead",
                        title=f"New Lead: {lead_name}",
                        description=f"Lead created - Status: {'Active' if latest_lead.is_active else 'Inactive'}",
                        timestamp=latest_lead.created_at,
                        metadata={
                            "is_active": latest_lead.is_active,
                            "phone": latest_lead.phone,
                            "email": latest_lead.email,
                            "lead_status_id": str(latest_lead.lead_status_id) if latest_lead.lead_status_id else None
                        }
                    ))

            # Process franchisor result
            if not isinstance(results[1], Exception):
                latest_franchisor = results[1].scalar_one_or_none()
                if latest_franchisor:
                    activities.append(RecentActivityItem(
                        id=str(latest_franchisor.id),
                        type="franchisor",
                        title=f"New Franchisor: {latest_franchisor.name}",
                        description=f"Franchisor added in {latest_franchisor.region or 'Unknown region'}",
                        timestamp=latest_franchisor.created_at,
                        metadata={
                            "region": latest_franchisor.region,
                            "budget": str(latest_franchisor.budget) if latest_franchisor.budget else None,
                            "is_active": latest_franchisor.is_active
                        }
                    ))

            # Process question result
            if not isinstance(results[2], Exception):
                latest_question = results[2].scalar_one_or_none()
                if latest_question:
                    activities.append(RecentActivityItem(
                        id=str(latest_question.id),
                        type="question",
                        title=f"Question: {latest_question.name}",
                        description="AI successfully answered user question",
                        timestamp=latest_question.created_at,
                        metadata={"type": "success", "ai_response": True}
                    ))

            # Process escalation result
            if not isinstance(results[3], Exception):
                latest_escalation = results[3].scalar_one_or_none()
                if latest_escalation:
                    activities.append(RecentActivityItem(
                        id=str(latest_escalation.id),
                        type="escalation",
                        title=f"Escalation: {latest_escalation.name}",
                    description=f"Question escalated - Status: {latest_escalation.support_status}",
                    timestamp=latest_escalation.created_at,
                    metadata={"type": "escalation", "status": latest_escalation.support_status}
                ))
            
            # Sort by timestamp descending and return top 4
            activities.sort(key=lambda x: x.timestamp, reverse=True)
            return activities[:4]
            
        except Exception as e:
            logger.error(f"Error getting recent activity: {e}", exc_info=True)
            raise DatabaseError(
                error_key="RECENT_ACTIVITY_FAILED",
                message="Failed to retrieve recent activity"
            )

    async def get_chart_data(self, start_date: datetime, end_date: datetime, time_period: str) -> List[ChartDataPoint]:
        """Get chart data for analytics visualization"""
        try:
            chart_data = []

            # Generate date range based on time period
            current_date = start_date
            while current_date <= end_date:
                # Calculate period end date
                if time_period == "day":
                    period_end = current_date + timedelta(days=1)
                elif time_period == "week":
                    period_end = current_date + timedelta(weeks=1)
                elif time_period == "month":
                    # Add one month
                    if current_date.month == 12:
                        period_end = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        period_end = current_date.replace(month=current_date.month + 1)
                elif time_period == "year":
                    period_end = current_date.replace(year=current_date.year + 1)
                else:
                    period_end = current_date + timedelta(days=1)

                # Ensure period_end doesn't exceed end_date
                period_end = min(period_end, end_date)

                # Get question bank count for this period
                question_query = select(func.count(QuestionBank.id)).where(
                    QuestionBank.is_deleted == False,
                    QuestionBank.created_at >= current_date,
                    QuestionBank.created_at < period_end
                )
                question_result = await self.db.execute(question_query)
                question_count = question_result.scalar() or 0

                # Get escalation count for this period
                escalation_query = select(func.count(EscalationQuestionBank.id)).where(
                    EscalationQuestionBank.is_deleted == False,
                    EscalationQuestionBank.created_at >= current_date,
                    EscalationQuestionBank.created_at < period_end
                )
                escalation_result = await self.db.execute(escalation_query)
                escalation_count = escalation_result.scalar() or 0

                # Calculate total and escalation rate
                total_questions = question_count + escalation_count
                escalation_rate = (escalation_count / total_questions * 100) if total_questions > 0 else 0.0

                chart_data.append(ChartDataPoint(
                    date=self._format_date_for_period(current_date, time_period),
                    question_count=total_questions,
                    escalation_count=escalation_count,
                    escalation_rate=round(escalation_rate, 1)
                ))

                # Move to next period
                if time_period == "day":
                    current_date += timedelta(days=1)
                elif time_period == "week":
                    current_date += timedelta(weeks=1)
                elif time_period == "month":
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1)
                elif time_period == "year":
                    current_date = current_date.replace(year=current_date.year + 1)
                else:
                    current_date += timedelta(days=1)

                # Break if we've exceeded the end date
                if current_date > end_date:
                    break

            return chart_data

        except Exception as e:
            logger.error(f"Error getting chart data: {e}", exc_info=True)
            raise DatabaseError(
                error_key="CHART_DATA_FAILED",
                message="Failed to retrieve chart data"
            )

    async def get_detailed_analytics(self, start_date: datetime, end_date: datetime, time_period: str) -> List[DetailedAnalyticsRow]:
        """Get detailed analytics table data"""
        try:
            # Reuse chart data logic for detailed analytics
            chart_data = await self.get_chart_data(start_date, end_date, time_period)

            # Convert chart data to detailed analytics rows
            detailed_analytics = []
            for data_point in chart_data:
                detailed_analytics.append(DetailedAnalyticsRow(
                    date=data_point.date,
                    question_count=data_point.question_count,
                    escalation_count=data_point.escalation_count,
                    escalation_rate=data_point.escalation_rate
                ))

            return detailed_analytics

        except Exception as e:
            logger.error(f"Error getting detailed analytics: {e}", exc_info=True)
            raise DatabaseError(
                error_key="DETAILED_ANALYTICS_FAILED",
                message="Failed to retrieve detailed analytics"
            )

    async def get_dashboard_analytics(
        self,
        date_range: str = "last_7_days",
        time_period: str = "day",
        custom_start: Optional[datetime] = None,
        custom_end: Optional[datetime] = None
    ) -> DashboardAnalyticsResponse:
        """Get complete dashboard analytics data"""
        try:
            # Get date range
            start_date, end_date = self._get_date_range(date_range, custom_start, custom_end)

            # Get all analytics data
            counts = await self.get_analytics_counts(start_date, end_date)
            recent_activity = await self.get_recent_activity()
            chart_data = await self.get_chart_data(start_date, end_date, time_period)
            detailed_analytics = await self.get_detailed_analytics(start_date, end_date, time_period)

            # Create filter information
            applied_filters = {
                "date_range": date_range,
                "time_period": time_period,
                "start_date": start_date.isoformat() if custom_start else None,
                "end_date": end_date.isoformat() if custom_end else None
            }

            available_filters = AnalyticsFilterOptions(
                date_ranges=["last_7_days", "last_30_days", "last_3_months", "last_6_months", "last_year", "custom"],
                time_periods=["day", "week", "month", "year"]
            )

            # Create period label
            period_labels = {
                "last_7_days": "Last 7 days",
                "last_30_days": "Last 30 days",
                "last_3_months": "Last 3 months",
                "last_6_months": "Last 6 months",
                "last_year": "Last year",
                "custom": f"Custom period ({start_date.strftime('%b %d')} - {end_date.strftime('%b %d')})"
            }
            period_label = period_labels.get(date_range, "Custom period")

            return DashboardAnalyticsResponse(
                counts=counts,
                recent_activity=recent_activity,
                chart_data=chart_data,
                detailed_analytics=detailed_analytics,
                applied_filters=applied_filters,
                available_filters=available_filters,
                period_label=period_label,
                generated_at=datetime.now()
            )

        except Exception as e:
            logger.error(f"Error getting dashboard analytics: {e}", exc_info=True)
            raise DatabaseError(
                error_key="DASHBOARD_ANALYTICS_FAILED",
                message="Failed to retrieve dashboard analytics"
            )
