"""
Enhanced Agent Processing Service
Provides advanced document processing using the agent system
"""

import asyncio
from typing import Dict, Any, Optional, List
import structlog
from datetime import datetime

from app.agents.document_ingestion import DocumentIngestionAgent
from app.agents.question_answering import QuestionAnsweringAgent
from app.agents.base import Agent<PERSON>onfig, AgentResponse, AgentRole
from app.core.database.connection import get_db
from app.models.franchisor import Franchisor
from sqlalchemy import select

logger = structlog.get_logger()


class EnhancedAgentProcessor:
    """Enhanced document processing using multi-agent system"""
    
    def __init__(self):
        self.ingestor_agent = None
        self.rag_agent = None
        self._initialized = False
    
    async def _initialize_agents(self):
        """Initialize agents if not already done"""
        if self._initialized:
            return
        
        try:
            # Initialize Document Ingestor Agent
            ingestor_config = AgentConfig(
                role=AgentRole.DOCUMENT_INGESTION,  # Use correct enum value
                name="enhanced_document_ingestor",  # Add required name field
                model="gpt-4",
                temperature=0.1,
                max_tokens=2000,
                description="Advanced document ingestion and processing"
            )
            self.ingestor_agent = DocumentIngestionAgent(ingestor_config)

            # Initialize RAG Agent (using QuestionAnsweringAgent for RAG functionality)
            rag_config = AgentConfig(
                role=AgentRole.QUESTION_ANSWERING,  # Use correct enum value
                name="enhanced_rag_processor",  # Add required name field
                model="gpt-4",
                temperature=0.2,
                max_tokens=1500,
                description="Enhanced RAG processing and optimization"
            )
            self.rag_agent = QuestionAnsweringAgent(rag_config)
            
            self._initialized = True
            logger.info("Enhanced agent processor initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize enhanced agent processor", error=str(e))
            raise
    
    async def process_franchisor_document(
        self,
        franchisor_id: str,
        document_url: str,
        processing_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process franchisor document with enhanced agent capabilities
        
        Args:
            franchisor_id: Franchisor ID
            document_url: Document URL to process
            processing_options: Additional processing options
            
        Returns:
            Processing results with enhanced features
        """
        try:
            await self._initialize_agents()
            
            logger.info("Starting enhanced agent processing",
                       franchisor_id=franchisor_id,
                       document_url=document_url)
            
            # Get franchisor information
            franchisor_info = await self._get_franchisor_info(franchisor_id)
            if not franchisor_info:
                raise ValueError(f"Franchisor not found: {franchisor_id}")
            
            # Step 1: Enhanced Document Ingestion
            ingestion_result = await self._enhanced_ingestion(
                franchisor_id=franchisor_id,
                document_url=document_url,
                franchisor_info=franchisor_info,
                options=processing_options or {}
            )
            
            if not ingestion_result.success:
                logger.error("Enhanced ingestion failed", 
                           error=ingestion_result.error,
                           franchisor_id=franchisor_id)
                return {
                    "success": False,
                    "error": f"Enhanced ingestion failed: {ingestion_result.error}",
                    "agent_processing": False
                }
            
            # Step 2: Enhanced RAG Processing
            rag_result = await self._enhanced_rag_processing(
                franchisor_id=franchisor_id,
                ingestion_data=ingestion_result.data,
                franchisor_info=franchisor_info
            )
            
            if not rag_result.success:
                logger.warning("Enhanced RAG processing failed", 
                             error=rag_result.error,
                             franchisor_id=franchisor_id)
                # Continue even if RAG enhancement fails
            
            # Compile results
            result = {
                "success": True,
                "agent_processing": True,
                "franchisor_id": franchisor_id,
                "document_url": document_url,
                "processed_at": datetime.utcnow().isoformat(),
                "ingestion": {
                    "success": ingestion_result.success,
                    "chunks_processed": ingestion_result.data.get("chunks_processed", 0),
                    "enhanced_features": ingestion_result.data.get("enhanced_features", []),
                    "processing_time": ingestion_result.execution_time
                },
                "rag_enhancement": {
                    "success": rag_result.success if rag_result else False,
                    "optimizations_applied": rag_result.data.get("optimizations", []) if rag_result and rag_result.success else [],
                    "processing_time": rag_result.execution_time if rag_result else 0
                }
            }
            
            logger.info("Enhanced agent processing completed successfully",
                       franchisor_id=franchisor_id,
                       chunks_processed=result["ingestion"]["chunks_processed"],
                       enhanced_features=len(result["ingestion"]["enhanced_features"]))
            
            return result
            
        except Exception as e:
            logger.error("Enhanced agent processing failed",
                        franchisor_id=franchisor_id,
                        error=str(e))
            return {
                "success": False,
                "error": str(e),
                "agent_processing": False,
                "franchisor_id": franchisor_id
            }
    
    async def _get_franchisor_info(self, franchisor_id: str) -> Optional[Dict[str, Any]]:
        """Get franchisor information from database"""
        try:
            # Create direct database session for Celery worker context
            from app.core.database.connection import AsyncSessionLocal

            if not AsyncSessionLocal:
                logger.error("Database session not available")
                return None

            async with AsyncSessionLocal() as db:
                stmt = select(Franchisor).where(Franchisor.id == franchisor_id)
                result = await db.execute(stmt)
                franchisor = result.scalar_one_or_none()

                if not franchisor:
                    logger.warning("Franchisor not found in database", franchisor_id=franchisor_id)
                    return None

                logger.info("Successfully retrieved franchisor info",
                           franchisor_id=franchisor_id,
                           name=franchisor.name)

                return {
                    "id": str(franchisor.id),
                    "name": franchisor.name,
                    "industry_id": str(franchisor.industry_id) if franchisor.industry_id else None,
                    "industry_name": franchisor.industry_rel.name if franchisor.industry_rel else None,
                    "region": franchisor.region,
                    "budget": franchisor.budget,
                    "brochure_url": franchisor.brochure_url,
                    "email": franchisor.email,
                    "phone": franchisor.phone
                }
        except Exception as e:
            logger.error("Failed to get franchisor info",
                        franchisor_id=franchisor_id,
                        error=str(e))
            return None
    
    async def _enhanced_ingestion(
        self,
        franchisor_id: str,
        document_url: str,
        franchisor_info: Dict[str, Any],
        options: Dict[str, Any]
    ) -> AgentResponse:
        """Perform enhanced document ingestion using agent"""
        try:
            # Prepare context for the ingestor agent
            context = {
                "franchisor_info": franchisor_info,
                "document_url": document_url,
                "processing_options": options,
                "enhanced_features": [
                    "smart_chunking",
                    "context_aware_splitting",
                    "metadata_extraction",
                    "quality_assessment"
                ]
            }
            
            # Execute enhanced ingestion
            task = f"Process and ingest document for {franchisor_info['name']} with enhanced features"
            result = await self.ingestor_agent.execute(task, context)
            
            return result
            
        except Exception as e:
            logger.error("Enhanced ingestion failed", error=str(e))
            return AgentResponse(
                success=False,
                error=str(e),
                agent_id="document_ingestor",
                data={}
            )
    
    async def _enhanced_rag_processing(
        self,
        franchisor_id: str,
        ingestion_data: Dict[str, Any],
        franchisor_info: Dict[str, Any]
    ) -> Optional[AgentResponse]:
        """Perform enhanced RAG processing and optimization"""
        try:
            # Prepare context for RAG agent
            context = {
                "franchisor_info": franchisor_info,
                "ingestion_results": ingestion_data,
                "optimization_targets": [
                    "chunk_relevance",
                    "embedding_quality",
                    "retrieval_accuracy",
                    "answer_completeness"
                ]
            }
            
            # Execute RAG enhancement
            task = f"Optimize RAG system for {franchisor_info['name']} franchise information"
            result = await self.rag_agent.execute(task, context)
            
            return result
            
        except Exception as e:
            logger.error("Enhanced RAG processing failed", error=str(e))
            return AgentResponse(
                success=False,
                error=str(e),
                agent_id="rag_processor",
                data={}
            )


# Global instance
enhanced_processor = EnhancedAgentProcessor()
