"""
Sales Script schemas for request/response validation
"""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from enum import Enum

from app.schemas.base_response import StandardResponse


class ScriptStage(str, Enum):
    """Sales script stage enumeration"""
    INITIAL_GREETING = "initial_greeting"
    PREQUALIFICATION = "prequalification"
    DOCUMENT_QA = "document_qa"
    GOODBYE = "goodbye"


class SalesScriptBase(BaseModel):
    """Base sales script schema"""
    script_title: str = Field(..., description="Script title", max_length=100, example="Initial greeting")
    script_content: str = Field(..., description="Script content", example="Hi, this is <PERSON>, calling you on behalf of...")
    script_stage: ScriptStage = Field(..., description="Script stage", example="initial_greeting")
    order_sequence: int = Field(1, description="Order sequence", ge=1, example=1)
    has_variables: bool = Field(False, description="Whether script has variables")
    variable_schema: Optional[str] = Field(None, description="Variable schema definition")
    is_active: bool = Field(True, description="Whether the script is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "script_title": "Initial greeting",
                "script_content": "Hi, this is Kay, calling you on behalf of the Coochie Hydrogreen. You inquired about this opportunity. I want to give you information about this. Is this a good time to chat?",
                "script_stage": "initial_greeting",
                "order_sequence": 1,
                "has_variables": False,
                "variable_schema": None,
                "is_active": True
            }
        }
    )


class SalesScriptCreate(SalesScriptBase):
    """Schema for creating a sales script"""
    pass


class SalesScriptUpdate(BaseModel):
    """Schema for updating a sales script"""
    script_title: Optional[str] = Field(None, description="Script title", max_length=100)
    script_content: Optional[str] = Field(None, description="Script content")
    script_stage: Optional[ScriptStage] = Field(None, description="Script stage")
    order_sequence: Optional[int] = Field(None, description="Order sequence", ge=1)
    has_variables: Optional[bool] = Field(None, description="Whether script has variables")
    variable_schema: Optional[str] = Field(None, description="Variable schema definition")
    is_active: Optional[bool] = Field(None, description="Whether the script is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "script_title": "Updated Initial greeting",
                "script_content": "Updated script content...",
                "script_stage": "initial_greeting",
                "order_sequence": 1,
                "has_variables": False,
                "variable_schema": None,
                "is_active": True
            }
        }
    )


class SalesScriptResponse(BaseModel):
    """Schema for sales script response"""
    id: str = Field(..., description="Script ID", example="550e8400-e29b-41d4-a716-446655440000")
    script_title: str = Field(..., description="Script title", example="Initial greeting")
    script_content: str = Field(..., description="Script content")
    script_stage: str = Field(..., description="Script stage", example="initial_greeting")
    order_sequence: int = Field(..., description="Order sequence", example=1)
    has_variables: bool = Field(..., description="Whether script has variables")
    variable_schema: Optional[str] = Field(None, description="Variable schema definition")
    is_active: bool = Field(..., description="Whether the script is active")
    is_deleted: bool = Field(..., description="Whether the script is deleted")
    created_at: datetime = Field(..., description="Script creation timestamp")
    updated_at: datetime = Field(..., description="Script last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Script deletion timestamp")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "script_title": "Initial greeting",
                "script_content": "Hi, this is Kay, calling you on behalf of the Coochie Hydrogreen...",
                "script_stage": "initial_greeting",
                "order_sequence": 1,
                "has_variables": False,
                "variable_schema": None,
                "is_active": True,
                "is_deleted": False,
                "created_at": "2024-07-16T14:29:31.852000+00:00",
                "updated_at": "2024-07-16T14:29:31.852000+00:00",
                "deleted_at": None
            }
        }
    )


class SalesScriptListResponse(BaseModel):
    """Schema for sales script list response"""
    items: List[SalesScriptResponse] = Field(..., description="List of sales scripts")
    total_count: int = Field(..., description="Total number of sales scripts")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "550e8400-e29b-41d4-a716-446655440000",
                        "script_title": "Initial greeting",
                        "script_content": "Hi, this is Kay...",
                        "script_stage": "initial_greeting",
                        "order_sequence": 1,
                        "has_variables": False,
                        "variable_schema": None,
                        "is_active": True,
                        "is_deleted": False,
                        "created_at": "2024-07-16T14:29:31.852000+00:00",
                        "updated_at": "2024-07-16T14:29:31.852000+00:00",
                        "deleted_at": None
                    }
                ],
                "total_count": 4
            }
        }
    )


class SalesScriptSuccessResponse(StandardResponse):
    """Success response for single sales script"""
    data: SalesScriptResponse = Field(..., description="Sales script data")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": {
                    "title": "Sales Script Retrieved",
                    "description": "Sales script retrieved successfully"
                },
                "data": {
                    "id": "550e8400-e29b-41d4-a716-446655440000",
                    "script_title": "Initial greeting",
                    "script_content": "Hi, this is Kay...",
                    "script_stage": "initial_greeting",
                    "order_sequence": 1,
                    "has_variables": False,
                    "variable_schema": None,
                    "is_active": True,
                    "is_deleted": False,
                    "created_at": "2024-07-16T14:29:31.852000+00:00",
                    "updated_at": "2024-07-16T14:29:31.852000+00:00",
                    "deleted_at": None
                },
                "error_code": 0
            }
        }
    )


class SalesScriptListSuccessResponse(StandardResponse):
    """Success response for sales script list"""
    data: SalesScriptListResponse = Field(..., description="Sales script list data")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": {
                    "title": "Sales Scripts Retrieved",
                    "description": "Sales scripts retrieved successfully"
                },
                "data": {
                    "items": [
                        {
                            "id": "550e8400-e29b-41d4-a716-446655440000",
                            "script_title": "Initial greeting",
                            "script_content": "Hi, this is Kay...",
                            "script_stage": "initial_greeting",
                            "order_sequence": 1,
                            "has_variables": False,
                            "variable_schema": None,
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2024-07-16T14:29:31.852000+00:00",
                            "updated_at": "2024-07-16T14:29:31.852000+00:00",
                            "deleted_at": None
                        }
                    ],
                    "total_count": 4
                },
                "error_code": 0
            }
        }
    )
