from logging.config import fileConfig
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# Import settings to get DATABASE_URL
from app.core.config.settings import settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Set the database URL from environment, replacing asyncpg with psycopg2 for Alembic
alembic_database_url = settings.DATABASE_URL.replace('postgresql+asyncpg', 'postgresql')
config.set_main_option("sqlalchemy.url", alembic_database_url)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
from app.core.database.connection import Base

# Import all models for Alembic autogeneration
from app.models.user import User, RefreshToken
from app.models.session import Session
from app.models.otp import OTP
from app.models.franchisor import Franchisor
from app.models.lead import Lead, LeadResponse
from app.models.document import Document
from app.models.system_setting import SystemSetting
from app.models.category import Category
from app.models.sales_script import SalesScript
from app.models.pre_qualification_question import PreQualificationQuestion
from app.models.conversation_session import ConversationSession
from app.models.holiday import Holiday
from app.models.messaging_rule import MessagingRule

target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
